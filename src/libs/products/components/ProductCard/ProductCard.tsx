import { useState } from 'react';
import type { ProductType } from '@/types';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { Image, Text, Title } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { getPriceString } from '@/utils';
import { FavoriteButton } from '@/libs/products/components/FavoriteButton/FavoriteButton';
import { GpoRecommendedTag } from '@/libs/gpo/components/GpoRecommendedTag/GpoRecommendedTag';
import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { PurchaseHistory } from '@/libs/products/components/PurchaseHistory/PurchaseHistory';
import { SpecialInstructionIconList } from '@/libs/products/components/SpecialInstructionIconList/SpecialInstructionIconList';
import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';
import { AddToCartForm } from '@/libs/products/components/AddToCartForm/AddToCartForm';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { NetPriceRebateIcon } from '@/libs/products/components/NetPriceRebateIcon/NetPriceRebateIcon';
import styles from './ProductCard.module.css';

export type ProductCardProps = {
  data: ProductType;
};

export const ProductCard = ({ data }: ProductCardProps) => {
  const { id, imageUrl, name, offers, isFavorite, manufacturer } = data;
  const [currentOfferId, setCurrentOfferId] = useState(offers[0].id);
  const currentOffer = offers.find((offer) => currentOfferId === offer.id);

  if (!currentOffer) {
    return null;
  }

  const {
    id: productOfferId,
    isRecommended,
    vendorSku,
    lastOrderedAt,
    lastOrderedQuantity,
    increments,
  } = currentOffer;

  const { hasRebate, originalPrice, salePrice } =
    getProductOfferComputedData(currentOffer);
  const productUrl = getProductUrl(id, productOfferId);

  return (
    <div className={styles.container}>
      <div className={styles.imageContainer}>
        <GpoRecommendedTag isRecommended={isRecommended} top="0.75rem" />
        <Link to={productUrl} state={{ from: 'search' }}>
          <Image src={imageUrl} fallbackSrc={defaultProductImgUrl} alt={name} />
        </Link>
        <div className={styles.specialInstructionsWrap}>
          <SpecialInstructionIconList {...data} />
        </div>

        <div className={styles.favoriteButtonWrapper}>
          <FavoriteButton productId={id} isFavorite={isFavorite} />
        </div>
      </div>
      <Flex className={styles.infoWrap} direction="column" mt="1rem">
        <Link to={productUrl}>
          <Title order={3} mb="0">
            {name}
          </Title>
        </Link>
        <div className={styles.vendorInfo}>
          <p>
            <span>SKU:</span> {vendorSku}
          </p>
          {manufacturer ? <p>{manufacturer}</p> : null}
        </div>
        {lastOrderedAt ? (
          <div className="mb-2 w-full">
            <PurchaseHistory
              productId={currentOffer.id}
              lastOrderedAt={lastOrderedAt}
              lastOrderedQuantity={lastOrderedQuantity}
            />
          </div>
        ) : null}
        <VendorSwap
          currentOfferId={currentOffer.id}
          offers={offers}
          onSwap={setCurrentOfferId}
        />
      </Flex>

      <Flex align="center" mb="0.75rem" gap="0.5rem">
        {originalPrice > salePrice ? (
          <Flex align="flex-end" gap="10">
            <Text size="1.5rem" fw="500">
              {getPriceString(salePrice)}
            </Text>

            <Text
              size="sm"
              fw="500"
              td="line-through"
              c="rgba(51, 51, 51, 0.50)"
            >
              {getPriceString(originalPrice)}
            </Text>
          </Flex>
        ) : (
          <Text size="1.5rem" fw="500">
            {getPriceString(salePrice)}
          </Text>
        )}
        {hasRebate && <NetPriceRebateIcon />}
      </Flex>
      <AddToCartForm increments={increments} productOfferId={currentOfferId} />
    </div>
  );
};
