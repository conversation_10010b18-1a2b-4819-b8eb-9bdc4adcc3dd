import React from 'react';
import MagnifierIcon from './assets/magnifier.svg?react';
import MoreOptionsIcon from './assets/more-options.svg?react';

const iconComponents = {
  magnifier: MagnifierIcon,
  moreOptions: MoreOptionsIcon,
} as const;

type IconName = keyof typeof iconComponents;

interface IconProps extends React.SVGProps<SVGSVGElement> {
  name: IconName;
  size?: string | number;
  color?: string;
  className?: string;
  'aria-label'?: string;
}

export const Icon: React.FC<IconProps> = ({
  name,
  size = '1rem',
  color = 'currentColor',
  className,
  'aria-label': ariaLabel,
  ...restProps
}) => {
  const IconComponent = iconComponents[name];

  if (!IconComponent) {
    console.warn(
      `Icon "${name}" not found. Available icons:`,
      Object.keys(iconComponents),
    );
    return null;
  }

  return (
    <IconComponent
      width={size}
      height={size}
      color={color}
      className={className}
      aria-label={ariaLabel}
      {...restProps}
    />
  );
};
