import { ChangeEvent, KeyboardEvent, ReactNode } from 'react';
import clsx from 'clsx';

import { BaseInputField } from '@/components';

import styles from './HeaderSearch.module.css';
import { Icon } from '@/libs/icons/Icon';

interface Props {
  onEnter?: (value: string) => void;
  onChange?: (value: string) => void;
  onBlur?: (value: string) => void;
  value?: string;
  initialValue?: string;
  placeholder?: string;
  rootClassName?: string;
  size?: string;
  icon?: ReactNode;
}

export const HeaderSearch = ({
  onEnter,
  onChange,
  onBlur,
  value,
  initialValue,
  placeholder,
  rootClassName,
  size = 'md',
  icon,
}: Props) => {
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(event.target.value);
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && onEnter) {
      const input = event.target as HTMLInputElement;

      onEnter(input.value);
    }
  };

  const handleBlur = (event: ChangeEvent<HTMLInputElement>) => {
    const input = event.target as HTMLInputElement;

    if (onBlur) {
      onBlur(input.value);
    }
  };

  return (
    <div className={clsx(styles.root, rootClassName)}>
      <BaseInputField
        defaultValue={initialValue}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        data-testid="header-search-input"
        leftSection={
          icon ?? (
            <Icon
              name="magnifier"
              size="1rem"
              color="#667085"
              aria-label="Search"
            />
          )
        }
        placeholder={placeholder}
        size={size}
      />
    </div>
  );
};
