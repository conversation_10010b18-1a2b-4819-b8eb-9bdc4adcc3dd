import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { Menu, UnstyledButton } from '@mantine/core';
import { useNavigate } from 'react-router-dom';
import LogoutIcon from './assets/logout.svg?react';
import LockIcon from './assets/lock.svg?react';
import { MODAL_NAME } from '@/constants';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { ChangePasswordModal } from '../ChangePasswordModal/ChangePasswordModal';
import { Icon } from '@/libs/icons/Icon';

export const UserSectionMenu = () => {
  const { user, logout, leaveImpersonation } = useAuthStore();
  const { clearClinic } = useClinicStore();
  const navigate = useNavigate();
  const { openModal } = useModalStore();

  const handleLogout = () => {
    logout();
    clearClinic();
    navigate(SHOP_ROUTES_PATH.login);
  };

  const handleChangePassword = () => {
    openModal({ name: MODAL_NAME.CHANGE_PASSWORD });
  };

  const handleLeaveImpersonation = () => {
    leaveImpersonation();
  };

  return (
    <>
      <ChangePasswordModal />
      <Menu width={200} shadow="md" position="right-end">
        <Menu.Target>
          <UnstyledButton>
            <Icon
              name="moreOptions"
              color="#333"
              aria-label="User menu options"
            />
          </UnstyledButton>
        </Menu.Target>

        <Menu.Dropdown>
          <Menu.Item leftSection={<LockIcon />} onClick={handleChangePassword}>
            Change password
          </Menu.Item>
          {user?.isImpersonating ? (
            <Menu.Item
              leftSection={<LogoutIcon />}
              onClick={handleLeaveImpersonation}
            >
              Leave Impersonation
            </Menu.Item>
          ) : (
            <Menu.Item leftSection={<LogoutIcon />} onClick={handleLogout}>
              Logout
            </Menu.Item>
          )}
        </Menu.Dropdown>
      </Menu>
    </>
  );
};
