import React, { useState } from 'react';
import { Accordion, Text, AccordionValue } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';

import { BudgetLine } from '../BudgetLine';
import styles from './budget-details.module.css';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { BUDGET_TYPE } from '@/libs/cart/constants';

const CLASSES = {
  chevron: styles.arrowBox,
  control: styles.control,
  item: styles.item,
  label: styles.label,
};

export function getCapitalizeWord(word?: string) {
  if (!word) {
    return '';
  }

  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
}

export const BudgetDetails = () => {
  const { t } = useTranslation();
  const { budget } = useCartStore();
  const [accordionValue, setAccordionValue] =
    useState<AccordionValue<false>>('budget');
  const isDynamicBudget = budget?.type === BUDGET_TYPE.DYNAMIC;

  return (
    <div className={styles.container}>
      <Accordion
        variant="filled"
        defaultValue="budget"
        classNames={CLASSES}
        value={accordionValue}
        onChange={setAccordionValue}
      >
        <Accordion.Item value="budget">
          <Accordion.Control>
            <Flex>
              <Text span>{t('client.cart.budgetTitle')}</Text>
              <Text c="rgba(0, 0, 0, 0.15)" mx="10" span>
                |
              </Text>

              <Text
                component={Link}
                to={`${SHOP_ROUTES_PATH.settings}?tab=budget`}
              >
                {budget?.type
                  ? getCapitalizeWord(budget?.type)
                  : t('client.cart.setBudget')}
              </Text>
            </Flex>

            {accordionValue ? (
              <span>
                {budget?.type
                  ? t('client.cart.budgetText')
                  : t('client.cart.budgetEmptyText')}
              </span>
            ) : null}
          </Accordion.Control>

          <Accordion.Panel>
            <div className={styles.content}>
              <Text>{t('client.cart.weekly')}</Text>
              <div />
              <div />
              <Text>{t('client.cart.monthly')}</Text>
              <div />

              {budget?.weekToDate.map((item) => (
                <BudgetLine
                  key={`${item.category}-${item.spent}`}
                  name={t(`client.cart.${item.category}`)}
                  value={+item.spent}
                  budgetUsed={item.spentPercentage}
                  budgetUsedType="%"
                  maxValue={+item.target}
                  desc={t(`client.settings.budget.${item.category}Help`)}
                />
              ))}

              <div />

              {budget?.monthToDate.map((item) => (
                <BudgetLine
                  key={`${item.category}-${item.spent}`}
                  name={t(`client.cart.${item.category}`)}
                  value={isDynamicBudget ? +item.spentPercentage : +item.spent}
                  maxValue={
                    isDynamicBudget ? +item.targetPercentage : +item.target
                  }
                  isDynamicBudget={isDynamicBudget}
                  budgetUsed={
                    isDynamicBudget ? item.spent : item.spentPercentage
                  }
                  budgetUsedType={isDynamicBudget ? '$' : '%'}
                  desc={t(`client.settings.budget.${item.category}Help`)}
                />
              ))}
            </div>
          </Accordion.Panel>
        </Accordion.Item>
      </Accordion>
    </div>
  );
};
