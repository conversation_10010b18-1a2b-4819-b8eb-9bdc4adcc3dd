.modal {
  max-width: 600px !important;
  width: 90vw;
}

.container {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
}

.header {
  margin-bottom: 1.5rem;
}

.promoInfo {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.promoHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.promoIcon {
  width: 1rem;
  height: 1rem;
  color: #f59e0b;
}

.productsSection {
  margin-bottom: 1.5rem;
}

.productItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.productItem:last-child {
  border-bottom: none;
}

.productInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.productImage {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
}

.productImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholderImage {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.productDetails {
  flex: 1;
}

.productName {
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.productControls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.addToCartInputWrapper {
  min-width: 120px;
}

/* Removed old quantity controls - now using AddToCartInput */

.promoDetails {
  background: rgba(59, 130, 246, 0.05);
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.seeListLink {
  cursor: pointer;
  text-decoration: underline;
}

.seeListLink:hover {
  text-decoration: none;
}

.summary {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1rem;
  margin-bottom: 1.5rem;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.priceInfo {
  display: flex;
  align-items: center;
}

.footer {
  margin-top: 1.5rem;
}

.addToCartBtn {
  background: #fbbf24;
  border: none;
  color: #000;
  font-weight: 600;
  padding: 1rem;
  border-radius: 6px;
  transition: background-color 0.2s;
  width: 100%;
}

.addToCartBtn:hover:not(:disabled) {
  background: #f59e0b;
}

.addToCartBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
