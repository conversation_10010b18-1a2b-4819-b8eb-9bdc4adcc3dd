import { useState } from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { Dialog, DialogContent } from '@/libs/ui/Dialog/Dialog';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';
import { Text } from '@mantine/core';
import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';
import {
  AddToCartInput,
  type AddToCartInputProps,
} from '@/libs/products/components/AddToCartInput/AddToCartInput';
import styles from './PromoMatcherProducts.module.css';

export type PromoMatcherProductsProps = {
  promoType: string;
  title: string;
  description: string;
  products: Array<{
    id: string;
    name: string;
    sku: string;
    vendor: string;
    price: number;
    image?: string;
    increments?: number;
  }>;
  maxUnits?: number;
  subtotal: number;
  originalPrice: number;
};

type ProductQuantity = {
  [productId: string]: number;
};

export const PromoMatcherProducts = () => {
  const { modalOption, closeModal } = useModalStore();
  const {
    promoType,
    title,
    description,
    products = [],
    maxUnits = 10,
    subtotal,
    originalPrice,
  } = modalOption as unknown as PromoMatcherProductsProps;

  const [quantities, setQuantities] = useState<ProductQuantity>(
    products.reduce((acc, product) => {
      acc[product.id] = product.increments || 1;
      return acc;
    }, {} as ProductQuantity),
  );

  // Check if the dialog should be open
  const isOpen =
    Boolean(modalOption.name) &&
    modalOption.name === MODAL_NAME.PROMO_MATCHER_PRODUCTS;

  if (!isOpen) {
    return null;
  }

  const handleQuantityUpdate =
    (productId: string): AddToCartInputProps['onUpdate'] =>
    ({ amount, setError }) => {
      if (amount > maxUnits) {
        setError(`Maximum ${maxUnits} units allowed`);
        return;
      }
      setQuantities((prev) => ({
        ...prev,
        [productId]: amount,
      }));
    };

  const getTotalItems = () => {
    return Object.values(quantities).reduce((sum, qty) => sum + qty, 0);
  };

  const handleAddToCart = () => {
    // TODO: Implement add to cart logic
    console.log('Adding to cart:', quantities);
    closeModal();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent className={styles.modal}>
        <div className={styles.container}>
          {/* Header */}
          <div className={styles.header}>
            <Text size="xl" fw="600" mb="xs">
              You're Almost There!
            </Text>
            <Text size="sm" c="dimmed" mb="lg">
              Follow the steps below to claim your savings before this offer
              expires.
            </Text>
          </div>

          {/* Promo Info */}
          <div className={styles.promoInfo}>
            <div className={styles.promoHeader}>
              <DiscountIcon className={styles.promoIcon} />
              <Text size="sm" fw="500">
                Promotion • {promoType}
              </Text>
            </div>
            <Text size="lg" fw="600" mt="xs">
              {title}
            </Text>
            {description && (
              <Text size="sm" c="dimmed" mt="xs">
                {description}
              </Text>
            )}
          </div>

          {/* Products */}
          <div className={styles.productsSection}>
            {products.map((product) => (
              <div key={product.id} className={styles.productItem}>
                <div className={styles.productInfo}>
                  <div className={styles.productImage}>
                    {product.image ? (
                      <img src={product.image} alt={product.name} />
                    ) : (
                      <div className={styles.placeholderImage} />
                    )}
                  </div>
                  <div className={styles.productDetails}>
                    <Text size="sm" fw="500" className={styles.productName}>
                      {product.name}
                    </Text>
                    <Text size="xs" c="dimmed">
                      SKU: {product.sku}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {product.vendor}
                    </Text>
                  </div>
                </div>

                <div className={styles.productControls}>
                  <Text size="lg" fw="600" mr="md">
                    ${product.price.toFixed(2)}
                  </Text>
                  <div className={styles.addToCartInputWrapper}>
                    <AddToCartInput
                      originalAmount={product.increments || 1}
                      minIncrement={product.increments || 1}
                      onUpdate={handleQuantityUpdate(product.id)}
                      size="sm"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Promo Details */}
          <div className={styles.promoDetails}>
            <Text size="sm" fw="500">
              Buy now and get{' '}
              <Text span fw="700" c="blue">
                9 for free!
              </Text>{' '}
              <Text span c="blue" className={styles.seeListLink}>
                See List
              </Text>
            </Text>
            <Text size="xs" c="dimmed">
              Max: {maxUnits} Units
            </Text>
          </div>

          {/* Summary */}
          <div className={styles.summary}>
            <div className={styles.summaryRow}>
              <Text size="sm">Subtotal ({getTotalItems()} items)</Text>
              <div className={styles.priceInfo}>
                <Text size="xl" fw="700">
                  ${subtotal.toFixed(2)}
                </Text>
                <Text size="sm" c="dimmed" td="line-through" ml="xs">
                  ${originalPrice.toFixed(2)}
                </Text>
              </div>
            </div>
          </div>

          {/* Add to Cart Button */}
          <div className={styles.footer}>
            <Button
              size="md"
              onClick={handleAddToCart}
              disabled={getTotalItems() === 0}
              className={styles.addToCartBtn}
            >
              Add to Cart
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
