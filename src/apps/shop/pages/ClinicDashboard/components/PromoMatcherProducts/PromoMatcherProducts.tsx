import { useState } from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { Dialog, DialogContent } from '@/libs/ui/Dialog/Dialog';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';
import { Text } from '@mantine/core';
import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';
import {
  AddToCartInput,
  type AddToCartInputProps,
} from '@/libs/products/components/AddToCartInput/AddToCartInput';

export type PromoMatcherProductsProps = {
  promoType: string;
  title: string;
  description: string;
  products: Array<{
    id: string;
    name: string;
    sku: string;
    vendor: string;
    price: number;
    image?: string;
    increments?: number;
  }>;
  maxUnits?: number;
  subtotal: number;
  originalPrice: number;
};

type ProductQuantity = {
  [productId: string]: number;
};

export const PromoMatcherProducts = () => {
  const { modalOption, closeModal } = useModalStore();
  const {
    promoType,
    title,
    description,
    products = [],
    maxUnits = 10,
    subtotal,
    originalPrice,
  } = modalOption as unknown as PromoMatcherProductsProps;

  const [quantities, setQuantities] = useState<ProductQuantity>(
    products.reduce((acc, product) => {
      acc[product.id] = product.increments || 1;
      return acc;
    }, {} as ProductQuantity),
  );

  // Check if the dialog should be open
  const isOpen =
    Boolean(modalOption.name) &&
    modalOption.name === MODAL_NAME.PROMO_MATCHER_PRODUCTS;

  if (!isOpen) {
    return null;
  }

  const handleQuantityUpdate =
    (productId: string): AddToCartInputProps['onUpdate'] =>
    ({ amount, setError }) => {
      if (amount > maxUnits) {
        setError(`Maximum ${maxUnits} units allowed`);
        return;
      }
      setQuantities((prev) => ({
        ...prev,
        [productId]: amount,
      }));
    };

  const getTotalItems = () => {
    return Object.values(quantities).reduce((sum, qty) => sum + qty, 0);
  };

  const handleAddToCart = () => {
    // TODO: Implement add to cart logic
    console.log('Adding to cart:', quantities);
    closeModal();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent className="w-[90vw] !max-w-[600px]">
        <div className="rounded-lg bg-white p-6">
          {/* Header */}
          <div className="mb-6">
            <Text size="xl" fw="600" mb="xs">
              You're Almost There!
            </Text>
            <Text size="sm" c="dimmed" mb="lg">
              Follow the steps below to claim your savings before this offer
              expires.
            </Text>
          </div>

          {/* Promo Info */}
          <div className="mb-6 rounded-lg bg-black/[0.02] p-4">
            <div className="mb-2 flex items-center gap-2">
              <DiscountIcon className="h-4 w-4 text-amber-500" />
              <Text size="sm" fw="500">
                Promotion • {promoType}
              </Text>
            </div>
            <Text size="lg" fw="600" mt="xs">
              {title}
            </Text>
            {description && (
              <Text size="sm" c="dimmed" mt="xs">
                {description}
              </Text>
            )}
          </div>

          {/* Products */}
          <div className="mb-6">
            {products.map((product) => (
              <div
                key={product.id}
                className="flex items-center justify-between border-b border-black/10 py-4 last:border-b-0"
              >
                <div className="flex flex-1 items-center gap-4">
                  <div className="flex h-16 w-16 items-center justify-center overflow-hidden rounded bg-black/5">
                    {product.image ? (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="h-full w-full rounded bg-black/10" />
                    )}
                  </div>
                  <div className="flex-1">
                    <Text size="sm" fw="500" className="mb-1 leading-tight">
                      {product.name}
                    </Text>
                    <Text size="xs" c="dimmed">
                      SKU: {product.sku}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {product.vendor}
                    </Text>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <Text size="lg" fw="600" mr="md">
                    ${product.price.toFixed(2)}
                  </Text>
                  <div className="min-w-[120px]">
                    <AddToCartInput
                      originalAmount={product.increments || 1}
                      minIncrement={product.increments || 1}
                      onUpdate={handleQuantityUpdate(product.id)}
                      size="sm"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Promo Details */}
          <div className="mb-6 rounded-md bg-blue-500/5 p-4 text-center">
            <Text size="sm" fw="500">
              Buy now and get{' '}
              <Text span fw="700" c="blue">
                9 for free!
              </Text>{' '}
              <Text
                span
                c="blue"
                className="cursor-pointer underline hover:no-underline"
              >
                See List
              </Text>
            </Text>
            <Text size="xs" c="dimmed">
              Max: {maxUnits} Units
            </Text>
          </div>

          {/* Summary */}
          <div className="mb-6 border-t border-black/10 pt-4">
            <div className="flex items-center justify-between">
              <Text size="sm">Subtotal ({getTotalItems()} items)</Text>
              <div className="flex items-center">
                <Text size="xl" fw="700">
                  ${subtotal.toFixed(2)}
                </Text>
                <Text size="sm" c="dimmed" td="line-through" ml="xs">
                  ${originalPrice.toFixed(2)}
                </Text>
              </div>
            </div>
          </div>

          {/* Add to Cart Button */}
          <div className="mt-6">
            <Button
              size="md"
              onClick={handleAddToCart}
              disabled={getTotalItems() === 0}
              className="w-full rounded-md border-none bg-amber-400 p-4 font-semibold text-black transition-colors hover:bg-amber-500 disabled:cursor-not-allowed disabled:opacity-60"
            >
              Add to Cart
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
