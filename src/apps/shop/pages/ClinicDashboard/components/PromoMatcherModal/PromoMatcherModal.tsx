import { useState } from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { Modal } from '@/components';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';
import { Text } from '@mantine/core';
import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';

export type PromoMatcherModalProps = {
  promoType: string;
  title: string;
  description: string;
  products: Array<{
    id: string;
    name: string;
    sku: string;
    vendor: string;
    price: number;
    image?: string;
    increments?: number;
  }>;
  maxUnits?: number;
  subtotal: number;
  originalPrice: number;
};

type ProductQuantity = {
  [productId: string]: number;
};

// Base component that receives HOC props
const PromoMatcherModalBase = ({
  addToCartState,
  addToCartComponents,
}: WithAddToCartProps) => {
  const { modalOption, closeModal } = useModalStore();
  const {
    promoType,
    title,
    description,
    products = [],
    maxUnits = 10,
    subtotal,
    originalPrice,
  } = modalOption as unknown as PromoMatcherModalProps;

  const [quantities, setQuantities] = useState<ProductQuantity>(
    products.reduce((acc, product) => {
      acc[product.id] = product.increments || 1;
      return acc;
    }, {} as ProductQuantity),
  );

  // Check if the modal should be open
  const isOpen =
    Boolean(modalOption.name) &&
    modalOption.name === MODAL_NAME.PROMO_MATCHER_PRODUCTS;

  if (!isOpen) {
    return null;
  }

  const { handleAddToCartClick, handleQuantityUpdate } = addToCartState;
  const { AddToCartInput, AddToCartButton } = addToCartComponents;

  const handleQuantityUpdateForProduct =
    (productId: string) =>
    ({ amount, setError }: Parameters<typeof handleQuantityUpdate>[0]) => {
      if (amount > maxUnits) {
        setError(`Maximum ${maxUnits} units allowed`);
        return;
      }
      setQuantities((prev) => ({
        ...prev,
        [productId]: amount,
      }));
    };

  const getTotalItems = () => {
    return Object.values(quantities).reduce((sum, qty) => sum + qty, 0);
  };

  const handleAddToCart = () => {
    // TODO: Implement add to cart logic for multiple products
    console.log('Adding to cart:', quantities);
    closeModal();
  };

  return (
    <Modal name={MODAL_NAME.PROMO_MATCHER_PRODUCTS} size="lg" withCloseButton>
      <div className="rounded-lg bg-white p-6">
        {/* Header */}
        <div className="mb-6">
          <Text size="xl" fw="600" mb="xs">
            You're Almost There!
          </Text>
          <Text size="sm" c="dimmed" mb="lg">
            Follow the steps below to claim your savings before this offer
            expires.
          </Text>
        </div>

        {/* Promo Info */}
        <div className="mb-6 rounded-lg bg-black/[0.02] p-4">
          <div className="mb-2 flex items-center gap-2">
            <DiscountIcon className="h-4 w-4 text-amber-500" />
            <Text size="sm" fw="500">
              Promotion • {promoType}
            </Text>
          </div>
          <Text size="lg" fw="600" mt="xs">
            {title}
          </Text>
          {description && (
            <Text size="sm" c="dimmed" mt="xs">
              {description}
            </Text>
          )}
        </div>

        {/* Products */}
        <div className="mb-6">
          {products.map((product) => (
            <div
              key={product.id}
              className="flex items-center justify-between border-b border-black/10 py-4 last:border-b-0"
            >
              <div className="flex flex-1 items-center gap-4">
                <div className="flex h-16 w-16 items-center justify-center overflow-hidden rounded bg-black/5">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="h-full w-full rounded bg-black/10" />
                  )}
                </div>
                <div className="flex-1">
                  <Text size="sm" fw="500" className="mb-1 leading-tight">
                    {product.name}
                  </Text>
                  <Text size="xs" c="dimmed">
                    SKU: {product.sku}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {product.vendor}
                  </Text>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Text size="lg" fw="600" mr="md">
                  ${product.price.toFixed(2)}
                </Text>
                <div className="min-w-[120px]">
                  <AddToCartInput
                    originalAmount={product.increments || 1}
                    minIncrement={product.increments || 1}
                    onUpdate={handleQuantityUpdateForProduct(product.id)}
                    size="sm"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Promo Details */}
        <div className="mb-6 rounded-md bg-blue-500/5 p-4 text-center">
          <Text size="sm" fw="500">
            Buy now and get{' '}
            <Text span fw="700" c="blue">
              9 for free!
            </Text>{' '}
            <Text
              span
              c="blue"
              className="cursor-pointer underline hover:no-underline"
            >
              See List
            </Text>
          </Text>
          <Text size="xs" c="dimmed">
            Max: {maxUnits} Units
          </Text>
        </div>

        {/* Summary */}
        <div className="mb-6 border-t border-black/10 pt-4">
          <div className="flex items-center justify-between">
            <Text size="sm">Subtotal ({getTotalItems()} items)</Text>
            <div className="flex items-center">
              <Text size="xl" fw="700">
                ${subtotal.toFixed(2)}
              </Text>
              <Text size="sm" c="dimmed" td="line-through" ml="xs">
                ${originalPrice.toFixed(2)}
              </Text>
            </div>
          </div>
        </div>

        {/* Add to Cart Button */}
        <div className="mt-6">
          <Button
            size="md"
            onClick={handleAddToCart}
            disabled={getTotalItems() === 0}
            className="w-full rounded-md border-none bg-amber-400 p-4 font-semibold text-black transition-colors hover:bg-amber-500 disabled:cursor-not-allowed disabled:opacity-60"
          >
            Add to Cart
          </Button>
        </div>
      </div>
    </Modal>
  );
};

// For now, we'll create a simple version without the HOC since we need specific product handling
export const PromoMatcherModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const {
    promoType,
    title,
    description,
    products = [],
    maxUnits = 10,
    subtotal,
    originalPrice,
  } = modalOption as unknown as PromoMatcherModalProps;

  const [quantities, setQuantities] = useState<ProductQuantity>(
    products.reduce((acc, product) => {
      acc[product.id] = product.increments || 1;
      return acc;
    }, {} as ProductQuantity),
  );

  // Check if the modal should be open
  const isOpen =
    Boolean(modalOption.name) &&
    modalOption.name === MODAL_NAME.PROMO_MATCHER_PRODUCTS;

  if (!isOpen) {
    return null;
  }

  const handleQuantityChange = (productId: string, change: number) => {
    setQuantities((prev) => {
      const currentQuantity = prev[productId] || 0;
      const newQuantity = Math.max(
        0,
        Math.min(maxUnits, currentQuantity + change),
      );
      return {
        ...prev,
        [productId]: newQuantity,
      };
    });
  };

  const getTotalItems = () => {
    return Object.values(quantities).reduce((sum, qty) => sum + qty, 0);
  };

  const handleAddToCart = () => {
    // TODO: Implement add to cart logic for multiple products
    console.log('Adding to cart:', quantities);
    closeModal();
  };

  return (
    <Modal name={MODAL_NAME.PROMO_MATCHER_PRODUCTS} size="lg" withCloseButton>
      <div className="rounded-lg bg-white p-6">
        {/* Header */}
        <div className="mb-6">
          <Text size="xl" fw="600" mb="xs">
            You're Almost There!
          </Text>
          <Text size="sm" c="dimmed" mb="lg">
            Follow the steps below to claim your savings before this offer
            expires.
          </Text>
        </div>

        {/* Promo Info */}
        <div className="mb-6 rounded-lg bg-black/[0.02] p-4">
          <div className="mb-2 flex items-center gap-2">
            <DiscountIcon className="h-4 w-4 text-amber-500" />
            <Text size="sm" fw="500">
              Promotion • {promoType}
            </Text>
          </div>
          <Text size="lg" fw="600" mt="xs">
            {title}
          </Text>
          {description && (
            <Text size="sm" c="dimmed" mt="xs">
              {description}
            </Text>
          )}
        </div>

        {/* Products */}
        <div className="mb-6">
          {products.map((product) => (
            <div
              key={product.id}
              className="flex items-center justify-between border-b border-black/10 py-4 last:border-b-0"
            >
              <div className="flex flex-1 items-center gap-4">
                <div className="flex h-16 w-16 items-center justify-center overflow-hidden rounded bg-black/5">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="h-full w-full rounded bg-black/10" />
                  )}
                </div>
                <div className="flex-1">
                  <Text size="sm" fw="500" className="mb-1 leading-tight">
                    {product.name}
                  </Text>
                  <Text size="xs" c="dimmed">
                    SKU: {product.sku}
                  </Text>
                  <Text size="xs" c="dimmed">
                    {product.vendor}
                  </Text>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Text size="lg" fw="600" mr="md">
                  ${product.price.toFixed(2)}
                </Text>
                <div className="flex items-center overflow-hidden rounded border border-black/20">
                  <button
                    className="cursor-pointer border-none bg-white px-3 py-2 text-base font-medium text-gray-700 hover:bg-black/5 disabled:cursor-not-allowed disabled:opacity-50"
                    onClick={() => handleQuantityChange(product.id, -1)}
                    disabled={quantities[product.id] <= 0}
                  >
                    −
                  </button>
                  <span className="min-w-12 border-r border-l border-black/10 px-4 py-2 text-center font-medium">
                    {quantities[product.id] || 0}
                  </span>
                  <button
                    className="cursor-pointer border-none bg-white px-3 py-2 text-base font-medium text-gray-700 hover:bg-black/5 disabled:cursor-not-allowed disabled:opacity-50"
                    onClick={() => handleQuantityChange(product.id, 1)}
                    disabled={quantities[product.id] >= maxUnits}
                  >
                    +
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Promo Details */}
        <div className="mb-6 rounded-md bg-blue-500/5 p-4 text-center">
          <Text size="sm" fw="500">
            Buy now and get{' '}
            <Text span fw="700" c="blue">
              9 for free!
            </Text>{' '}
            <Text
              span
              c="blue"
              className="cursor-pointer underline hover:no-underline"
            >
              See List
            </Text>
          </Text>
          <Text size="xs" c="dimmed">
            Max: {maxUnits} Units
          </Text>
        </div>

        {/* Summary */}
        <div className="mb-6 border-t border-black/10 pt-4">
          <div className="flex items-center justify-between">
            <Text size="sm">Subtotal ({getTotalItems()} items)</Text>
            <div className="flex items-center">
              <Text size="xl" fw="700">
                ${subtotal.toFixed(2)}
              </Text>
              <Text size="sm" c="dimmed" td="line-through" ml="xs">
                ${originalPrice.toFixed(2)}
              </Text>
            </div>
          </div>
        </div>

        {/* Add to Cart Button */}
        <div className="mt-6">
          <Button
            size="md"
            onClick={handleAddToCart}
            disabled={getTotalItems() === 0}
            className="w-full rounded-md border-none bg-amber-400 p-4 font-semibold text-black transition-colors hover:bg-amber-500 disabled:cursor-not-allowed disabled:opacity-60"
          >
            Add to Cart
          </Button>
        </div>
      </div>
    </Modal>
  );
};
