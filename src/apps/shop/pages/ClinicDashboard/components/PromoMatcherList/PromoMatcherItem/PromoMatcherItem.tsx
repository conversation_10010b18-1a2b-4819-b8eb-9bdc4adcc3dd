import { Button } from '@/libs/ui/Button/Button';
import { Divider } from '@mantine/core';
import DiscountIcon from '@/libs/gpo/components/SavingAlert/assets/discount.svg?react';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';
import styles from './PromoMatcherItem.module.css';

export type PromoMatcherItemProps = {
  promoType: string;
  title: string;
  description: string;
  vendor: string;
  category: string;
  endDate: string;
  savings?: string;
};

export const PromoMatcherItem = ({
  promoType,
  title,
  description,
  vendor,
  category,
  endDate,
  savings,
}: PromoMatcherItemProps) => {
  const { openModal } = useModalStore();

  const handleTakeDeal = () => {
    // Mock product data for the modal
    const mockProducts = [
      {
        id: '1',
        name: 'Pivetal® Suffusion™ K',
        sku: '159726',
        vendor: 'Boehringer Ingelheim',
        price: 60.0,
        increments: 1,
      },
      {
        id: '2',
        name: 'Pivetal® Suffusion™ K',
        sku: '159726',
        vendor: 'Boehringer Ingelheim',
        price: 60.0,
        increments: 1,
      },
    ];

    openModal({
      name: MODAL_NAME.PROMO_MATCHER_PRODUCTS,
      promoType,
      title,
      description,
      products: mockProducts,
      maxUnits: 10,
      subtotal: 120.0,
      originalPrice: 607.7,
    });
  };

  return (
    <div className="group relative rounded-sm border-1 border-black/2 bg-white p-5 shadow-xs hover:border-blue-400">
      <div className="flex items-center justify-between">
        <div className="flex-7">
          <div className="flex items-center gap-2">
            <DiscountIcon className="h-6 w-6" />
            <span className="text-sm font-medium text-black">
              Promotion • {promoType}
            </span>
          </div>
          <h4 className="text-lg font-semibold text-gray-900">{title}</h4>
          <p className="max-h-18 overflow-hidden pt-2 pb-1 text-xs tracking-wider text-black/80 transition-all duration-400 ease-in-out group-hover:max-h-72 hover:overflow-y-auto">
            {description}
          </p>
        </div>

        <div className="flex flex-3 justify-end">
          <Button
            size="sm"
            className={styles.takeDealBtn}
            onClick={handleTakeDeal}
          >
            Take Deal
          </Button>
        </div>
      </div>
      <Divider my="xs" />

      <div className="grid grid-cols-[auto_auto_auto_1fr] gap-3 divide-x-1 divide-solid divide-black/10">
        <span className="pr-2">
          <span className="mr-1 text-xs text-black/60">Vendor:</span>
          <span className="text-xs font-semibold">{vendor}</span>
        </span>
        <span className="pr-2">
          <span className="mr-1 text-xs text-black/60">Category:</span>
          <span className="text-xs font-semibold">{category}</span>
        </span>
        <span className="pr-2">
          <span className="mr-1 text-xs text-black/60">End Date:</span>
          <span className="text-xs font-semibold">{endDate}</span>
        </span>
        {savings && (
          <p className="ml-auto">
            <span className="mr-1 text-xs text-black/60">Savings:</span>
            <span className="text-xs font-semibold">{savings}</span>
          </p>
        )}
      </div>
    </div>
  );
};
