import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';

const PromoMatcherProduct = () => {
  return (
    <div className="grid h-[72px] w-full grid-cols-[auto_1fr_auto] items-center gap-6 bg-white p-4">
      <div className="h-12">
        <img
          src="https://staging.services.highfive.vet/storage/vendor-images/mwi.png"
          className="h-full"
        ></img>
      </div>
      <div>
        <p className="text-sm font-medium text-black">
          Pivetal® Suffusion™ K
        </p>
        <div className="flex items-center gap-4 divide-x-1 divide-solid divide-black/10">
          <span>
            <span className="text-xs text-black/65">SKU: </span>
            <span className="mr-3 text-xs font-medium text-black">159726</span>
          </span>
          <span className="text-xs text-black">Boehringer Ingelheim6</span>
        </div>
      </div>
      <div className="flex max-w-40 items-center gap-3">
        <span className="text-sm font-medium">$60.00</span>
        <AddToCartInput
          originalAmount={2}
          minIncrement={2}
          onUpdate={() => {}}
        />
      </div>
    </div>
  );
};

export default PromoMatcherProduct;
