import { AddToCartButton } from '@/libs/products/components/AddToCartForm/components/AddToCartButton/AddToCartButton';
import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { Button } from '@/libs/ui/Button/Button';
import styles from './PromoMatcherList.module.css';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { useState } from 'react';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import PromoMatcherProductsList from './PromoMatcherdProductsList/PromoMatcherdProductsList';
import PromoMatcherProduct from './PromoMatcherProduct/PromoMatcherProduct';
import PromoMatcherTitle from './PromoMatcherTitle/PromoMatcherTitle';
import Subtotal from './Subtotal/Subtotal';

export const PromoMatcherList = ({ productOfferId, increments }) => {
  // const { addToCart, updatingProductIds } = useCartStore();
  // const [amount, setAmount] = useState(increments ?? 1);
  // const cartProductMapState = useCartProductMapState();
  // const isUpdating = updatingProductIds.has(productOfferId);

  // const handleQuantityUpdate: AddToCartInputProps['onUpdate'] = ({
  //   amount: newAmount,
  // }) => {
  //   setAmount(newAmount);
  // };

  // const offerQuantityOnCart =
  //   cartProductMapState[productOfferId]?.quantity ?? 0;

  // const handleAddToCartClick: FormEventHandler<HTMLFormElement> = useCallback(
  //   (event) => {
  //     event.preventDefault();

  //     addToCart({
  //       productOfferId,
  //       quantity: offerQuantityOnCart + amount,
  //       // TODO: Handle error better
  //       onError: () => {},
  //     });
  //   },
  //   [productOfferId, addToCart, offerQuantityOnCart, amount],
  // );

  return (
    <div className="flex flex-col">
      <div className="flex-col bg-white p-6">
        <h3 className="mb-2 text-2xl font-medium">You&apos;re Almost There!</h3>
        <p className="mb-6 text-sm text-black/65">
          Follow the steps below to claim your savings before this offer
          expires.
        </p>
        <div className="space-y-4 rounded-lg border-2 border-black/10 bg-black/2.5 p-6">
          <PromoMatcherTitle />
          <div className="divider-h"></div>
          <div className="grid gap-2">
            <PromoMatcherProduct />
            <PromoMatcherProduct />
          </div>
          <p className="mr-2 mb-4 inline-block text-sm">
            Buy now and get <strong>9 for free!</strong>
          </p>
          <Button className={styles.seeList} variant="unstyled">
            See List
          </Button>
          <div className="divider-h"></div>
          <div className="flex items-center justify-between">
            <Subtotal />
            <div className="w-52">
              <AddToCartButton isLoading={false} fullWidth />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
