import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { Text, Title } from '@mantine/core';
import { FEATURE_FLAGS, MODAL_NAME } from '@/constants';
import { LastOrdersPanel } from './components/LastOrdersPanel/LastOrdersPanel';
import { EstimatedRebatesPanel } from './components/EstimatedRebatesPanel/EstimatedRebatesPanel';
import { OverviewPanel } from './components/OverviewPanel/OverviewPanel';
import { PromoMatcherList } from './components/PromoMatcherList/PromoMatcherList';
import { PromoMatcherProducts } from './components/PromoMatcherProducts';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { Button } from '@/libs/ui/Button/Button';

export const ClinicDashboard = () => {
  const { user } = useAuthStore();
  const { openModal } = useModalStore();

  const handleTestPromoModal = () => {
    const mockProducts = [
      {
        id: '1',
        name: 'Pivetal® Suffusion™ K',
        sku: '159726',
        vendor: 'Boehringer Ingelheim',
        price: 60.0,
        increments: 1,
      },
      {
        id: '2',
        name: 'Pivetal® Suffusion™ K',
        sku: '159726',
        vendor: 'Boehringer Ingelheim',
        price: 60.0,
        increments: 1,
      },
    ];

    openModal({
      name: MODAL_NAME.PROMO_MATCHER_PRODUCTS,
      promoType: 'Buy X Get Y Free',
      title:
        'Buy {xx} Pivetal® Suffusion™ products, get {xx} free. Kind for kind',
      description:
        'Follow the steps below to claim your savings before this offer expires.',
      products: mockProducts,
      maxUnits: 10,
      subtotal: 120.0,
      originalPrice: 607.7,
    });
  };

  return (
    <div className="main gap-10">
      <div>
        <Title order={3} size="h4" mb=".25rem">
          Welcome {user?.name}
        </Title>

        <Text size="md">
          View your clinics spend, rebate tracking, and order history below.
        </Text>
        <Button onClick={handleTestPromoModal} size="sm">
          Test Promo Modal
        </Button>
      </div>
      <PromoMatcherProducts />
    </div>
  );
};
