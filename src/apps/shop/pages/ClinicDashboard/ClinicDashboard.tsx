import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { PromoMatcherList } from './components/PromoMatcherList/PromoMatcherList';
import { useModalStore } from '@/apps/shop/stores/useModalStore';

export const ClinicDashboard = () => {
  // const handleTestPromoModal = () => {
  //   const mockProducts = [
  //     {
  //       id: '1',
  //       name: 'Pivetal® Suffusion™ K',
  //       sku: '159726',
  //       vendor: 'Boehringer Ingelheim',
  //       price: 60.0,
  //       increments: 1,
  //     },
  //     {
  //       id: '2',
  //       name: 'Pivetal® Suffusion™ K',
  //       sku: '159726',
  //       vendor: 'Boehringer Ingelheim',
  //       price: 60.0,
  //       increments: 1,
  //     },
  //   ];

  //   openModal({
  //     name: MODAL_NAME.PROMO_MATCHER_PRODUCTS,
  //     promoType: 'Buy X Get Y Free',
  //     title:
  //       'Buy {xx} Pivetal® Suffusion™ products, get {xx} free. Kind for kind',
  //     description:
  //       'Follow the steps below to claim your savings before this offer expires.',
  //     products: mockProducts,
  //     maxUnits: 10,
  //     subtotal: 120.0,
  //     originalPrice: 607.7,
  //   });
  // };

  return (
    <div className="main gap-10">
      <PromoMatcherList />
    </div>
  );
};
