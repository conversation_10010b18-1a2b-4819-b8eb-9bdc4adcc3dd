import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useClinicInfo } from '../../services/useClinicInfo';
import { PracticesForm } from './components/PracticesForm/PracticesForm';
import { Badge } from '@/libs/ui/Badge/Badge';
import {
  practiceTypeOptions,
  speciesFocusOptions,
} from './components/PracticesForm/constants';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { shoppingPreferencesOptions } from './constants';
import { FEATURE_FLAGS } from '@/constants';
import { Button } from '@/libs/ui/Button/Button';

interface PracticesProps {
  isEditing: boolean;
  onComplete: VoidFunction;
  startEditing: VoidFunction;
}
export const Practices = ({
  isEditing,
  startEditing,
  onComplete,
}: PracticesProps) => {
  const { clinicInfo, isLoading, fetchClinicInfo } = useClinicInfo();

  if (isLoading) {
    return (
      <div className="relative p-6">
        <ContentLoader />
      </div>
    );
  }

  if (!clinicInfo) {
    return null;
  }

  const practiceTypeMapState: Record<string, string> =
    practiceTypeOptions.reduce(
      (acc, { label, value }) => ({
        [value]: label,
        ...acc,
      }),
      {},
    );

  const speciesFocusMapState: Record<string, string> =
    speciesFocusOptions.reduce(
      (acc, { label, value }) => ({
        [value]: label,
        ...acc,
      }),
      {},
    );

  const {
    examRoomsCount,
    fulltimeDvmCount,
    speciesFocus,
    practiceTypes,
    primaryShoppingPreference,
    secondaryShoppingPreferences,
  } = clinicInfo;

  const shoppingPreferencesMapState: Record<string, string> =
    shoppingPreferencesOptions.reduce(
      (acc, { label, value }) => ({
        [value]: label,
        ...acc,
      }),
      {},
    );

  const defaultValues = {
    examRoomsCount: examRoomsCount || 0,
    fulltimeDvmCount: fulltimeDvmCount || 0,
    practiceTypes: practiceTypes || [],
    speciesFocus: Array.isArray(speciesFocus)
      ? speciesFocus[0] || ''
      : speciesFocus || '',
    ...(FEATURE_FLAGS.PURCHASING_PREFERENCES
      ? {
          primaryShoppingPreference: Array.isArray(primaryShoppingPreference)
            ? primaryShoppingPreference[0] || ''
            : primaryShoppingPreference || '',
          secondaryShoppingPreferences: secondaryShoppingPreferences || [],
        }
      : {}),
  };

  if (isEditing) {
    return (
      <PracticesForm
        clinicId={clinicInfo.id}
        onComplete={() => {
          fetchClinicInfo();
          onComplete();
        }}
        defaultValues={defaultValues}
      />
    );
  }

  const isPracticeInfoFilled = Boolean(speciesFocus && speciesFocus[0]);

  if (!isPracticeInfoFilled) {
    return (
      <Flex
        mt="md"
        py="40px"
        px="24px"
        direction="column"
        align="center"
        bg="#F8F9FB"
        style={{ borderRadius: '4px' }}
      >
        <Text size="16px" fw="500" ta="center" c="#344054">
          Customize Your Experience
        </Text>
        <Text size="14px" mt="8px" mb="16px" ta="center" c="#555F74" lh="1.5">
          Help us learn more about your clinic so we can tailor promotions that
          match your specific needs — minimizing noise and highlighting what
          truly matters.
        </Text>
        <Button
          onClick={startEditing}
          variant="secondary"
          style={{ width: 'auto' }}
        >
          Add Practice Settings
        </Button>
      </Flex>
    );
  }

  return (
    <>
      <div
        className="mt-3 bg-white p-6"
        style={{
          borderRadius: '4px',
          border: '1px solid #3958D4',
        }}
      >
        <Text size="14px" c="#344054" mb="12px">
          Species Focus
        </Text>
        <Text size="24px" c="#344054" fw="500">
          {speciesFocus ? speciesFocusMapState[speciesFocus[0]] : '--'}
        </Text>

        <Divider my="24px" />
        <Text size="14px" c="#344054" mb="12px">
          Type of Practice
        </Text>
        <Flex gap="0.5rem" wrap="wrap">
          {practiceTypes?.length
            ? practiceTypes.map((practiceType) => (
                <Badge
                  key={practiceType}
                  variant="default"
                  color="#3a6987"
                  background="#e5f1f9"
                >
                  {practiceTypeMapState[practiceType]}
                </Badge>
              ))
            : '--'}
        </Flex>

        <Divider my="24px" />
        <Flex align="center">
          <Text size="14px" c="#344054">
            Fulltime DVM:{' '}
            <Text fw="700" span>
              {fulltimeDvmCount || '--'}
            </Text>
          </Text>
          <Divider orientation="vertical" mx="24px" />
          <Text size="14px" c="#344054">
            Exam Rooms:{' '}
            <Text fw="700" span>
              {examRoomsCount || '--'}
            </Text>
          </Text>
        </Flex>

        {FEATURE_FLAGS.PURCHASING_PREFERENCES && (
          <div
            className="mt-6 p-6"
            style={{
              background:
                'linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.50) 100%), #F2F4F7',
              borderRadius: '4px',
              border: '1px solid rgba(0, 0, 0, 0.04)',
            }}
          >
            <Text c="#344054" size="14px" fw="500">
              Purchasing Preferences
            </Text>
            <Divider my="14px" />
            <div>
              <Text size="14px" mb="12px">
                Main Priority
              </Text>
              <Text c="#344054" size="16px" fw="500">
                {defaultValues.primaryShoppingPreference
                  ? shoppingPreferencesMapState[
                      defaultValues.primaryShoppingPreference
                    ]
                  : '--'}
              </Text>
            </div>
            <Divider my="14px" />
            <div>
              <Text size="14px" mb="12px">
                Secondary:
              </Text>
              <Flex gap="0.5rem" wrap="wrap">
                {secondaryShoppingPreferences?.length
                  ? secondaryShoppingPreferences?.map((preference) => (
                      <Badge
                        key={preference}
                        variant="default"
                        color="#3a6987"
                        background="#e5f1f9"
                      >
                        {shoppingPreferencesMapState[preference]}
                      </Badge>
                    ))
                  : '--'}
              </Flex>
            </div>
            <Divider my="14px" />
            <Text c="#666" size="12px" mt="12px">
              Attention, changing this preferences will change the way your
              offers are being display and the way they are being shared with
              you. Please, certify you are doing the right changes.
            </Text>
          </div>
        )}
      </div>
    </>
  );
};
