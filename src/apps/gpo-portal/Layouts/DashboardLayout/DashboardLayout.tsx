import { Flex, Loader } from '@mantine/core';
import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';

import { ErrorSection } from '@/components';
import { ProtectedLayout } from '@/apps/gpo-portal/Layouts/ProtectedLayout/ProtectedLayout';
import { AppContentWrap } from '@/libs/ui/AppContentWrap/AppContentWrap';
import { GpoSidebar } from '../../components/GpoSidebar/GpoSidebar';
import { GpoTopNavbar } from '../../components/GpoTopNavbar/GpoTopNavbar';

export const DashboardLayout = () => {
  return (
    <ErrorBoundary fallback={<ErrorSection />}>
      <Suspense
        fallback={
          <div className="loaderRoot height100vh">
            <Loader size="3rem" />
          </div>
        }
      >
        <ProtectedLayout>
          <AppContentWrap>
            <Flex>
              <GpoSidebar />
              <Flex direction="column" w="100%" ml="280px">
                <GpoTopNavbar />
                <ErrorBoundary fallback={<ErrorSection />}>
                  <Outlet />
                </ErrorBoundary>
              </Flex>
            </Flex>
          </AppContentWrap>
        </ProtectedLayout>
      </Suspense>
    </ErrorBoundary>
  );
};
