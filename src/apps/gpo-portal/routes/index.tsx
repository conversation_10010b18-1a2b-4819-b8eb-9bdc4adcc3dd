import { lazy, LazyExoticComponent } from 'react';
import { createBrowserRouter } from 'react-router-dom';
import { AuthLayout } from '@/libs/auth/components/AuthLayout/AuthLayout';
import { DashboardLayout } from '../Layouts/DashboardLayout/DashboardLayout';

const pages = [
  'Login',
  'ForgotPassword',
  'ChangePassword',
  'Dashboard',
] as const;
type PageName = (typeof pages)[number];

const pageModules: Record<
  PageName,
  LazyExoticComponent<() => JSX.Element>
> = pages.reduce(
  (acc, pageName) => ({
    ...acc,
    [pageName]: lazy(() =>
      import(`@/apps/gpo-portal/pages/${pageName}/${pageName}.tsx`).then(
        (pageModule) => ({
          default: pageModule[pageName] as React.FC,
        }),
      ),
    ),
  }),
  {} as Record<PageName, LazyExoticComponent<() => JSX.Element>>,
);

const { Login, ForgotPassword, ChangePassword, Dashboard } = pageModules;

export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthLayout
        bg="linear-gradient(180deg, #7a8ba8 0%, #1a3a6b 100%)"
        innerBg="#fff"
      />
    ),
    children: [
      { index: true, element: <Login /> },
      { path: 'login', element: <Login /> },
      { path: 'forgot-password', element: <ForgotPassword /> },
      { path: 'change-password', element: <ChangePassword /> },
    ],
  },
  {
    path: '/dashboard',
    element: <DashboardLayout />,
    children: [{ index: true, element: <Dashboard /> }],
  },
]);
